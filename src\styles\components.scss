/* -----------------------------------------
 * 主题与基础配置
 * ----------------------------------------- */
// 主题变量
$config: (
  sizes: (
    header: 48px, tabs: 40px, footer: 48px, spacing: 8px, radius: 4px, sidebar: 300px
  ),
  colors: (
    primary: var(--b3-theme-primary), primary-lighter: var(--b3-theme-primary-lighter),
    surface: var(--b3-theme-surface), surface-lighter: var(--b3-theme-surface-lighter),
    background: var(--b3-theme-background), border: var(--b3-border-color),
    on-background: var(--b3-theme-on-background), on-surface: var(--b3-theme-on-surface),
    on-surface-variant: var(--b3-theme-on-surface-variant), on-primary: var(--b3-theme-on-primary)
  )
);

// 辅助函数
$transition: 0.3s ease;
@function size($key) { @return map-get(map-get($config, sizes), $key); }
@function color($key) { @return map-get(map-get($config, colors), $key); }

// 通用mixins
@mixin text-ellipsis { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
@mixin flex($direction: row, $justify: flex-start, $align: stretch, $gap: 0) {
  display: flex; flex-direction: $direction; justify-content: $justify; align-items: $align;
  @if $gap != 0 { gap: $gap; }
}
@mixin position-absolute($top: null, $right: null, $bottom: null, $left: null) {
  position: absolute;
  @if $top != null { top: $top; } @if $right != null { right: $right; }
  @if $bottom != null { bottom: $bottom; } @if $left != null { left: $left; }
}
@mixin bordered($color: color(border)) { border: 1px solid $color; border-radius: size(radius); }
@mixin standard-text($size: 14px, $weight: normal) {
  font-size: $size; font-weight: $weight; color: color(on-background);
}
@mixin interactive($hover-bg: null) {
  cursor: pointer; transition: all 0.2s;
  &:hover {
    @if $hover-bg { background: $hover-bg; }
    border-color: color(primary); color: color(primary);
  }
}

/* -----------------------------------------
 * 基础UI组件
 * ----------------------------------------- */
// 状态样式
%hidden-state { opacity: 0; pointer-events: none; }
%hover-active {
  &:hover { border-color: color(primary); }
  &.active, &.playing { background: color(primary-lighter); border-color: color(primary); }
}

// 基础元素
%button-base {
  padding: 6px; border: none; background: none; border-radius: size(radius);
  @include interactive(color(surface-lighter));
}

%input-base {
  padding: 4px 8px; border-radius: size(radius); background: color(background); 
  color: color(on-background); outline: none; font-size: 14px; line-height: 18px;
  border: 1px solid color(border); min-width: 140px; height: 20px;
  &:focus, &:hover { border-color: color(primary); }
}

%section-container {
  flex: 1; overflow-y: auto; padding: size(spacing); background: color(background);
}

%item-base {
  @include bordered; background: color(surface); position: relative;
  @include interactive; @extend %hover-active; 
}

%item-title {
  @include standard-text(14px, 500); @include text-ellipsis; transition: color 0.2s;
}

// 缩略图 - 统一简化样式
@mixin thumbnail($width: 120px, $height: 68px) {
  position: relative; width: $width; height: $height; border-radius: calc(size(radius) - 1px); 
  overflow: hidden; flex-shrink: 0; @include flex(row, center, center); 
  background: color(surface-lighter);
  
  img { width: 100%; height: 100%; object-fit: cover; }
  .duration { 
    @include position-absolute(null, 4px, 4px, null); padding: 2px 4px; 
    background: rgba(0, 0, 0, 0.7); color: #fff; font-size: 12px; border-radius: 2px; 
  }
}

/* -----------------------------------------
 * 播放器布局
 * ----------------------------------------- */
// 基础面板
%panel-base {
  @include position-absolute(0, 0, 0, 0); @include flex(column);
  @include bordered(color(surface-lighter)); background: color(background); 
  box-sizing: border-box;
}

// 媒体播放器
.media-player-tab {
  @include position-absolute(0, 0, 0, 0); padding: size(spacing);
  box-sizing: border-box; background: color(background);
  
  .content-area {
    position: relative; width: 100%; height: 100%;
    @include flex(row, flex-start, stretch, size(spacing)*0.5);
    
    .player-area {
      position: relative; border-radius: size(radius);
      background: color(surface); overflow: hidden; flex: 1;
    }
  }
}

// 播放器核心
.artplayer-app {
  position: relative; z-index: 2 !important; width: 100%; height: 100%; display: block;
  
  .art-video-player {
    width: 100% !important; height: 100% !important; background-color: #000;
    .art-controls { bottom: 0; }
    .art-subtitle { bottom: 65px !important; font-family: var(--b3-font-family); }
  }
  
  .floating-subtitle {
    position: absolute; left: 50%; bottom: 70px; transform: translateX(-50%);
    max-width: 80%; background: rgba(0, 0, 0, 0.6); color: #fff; padding: 5px 10px;
    border-radius: 4px; font-size: 16px; text-align: center; z-index: 30; pointer-events: none;
  }
}

// 提示修复
.protyle [class*=hint--]:not(.fn__none) {
  font-family: var(--b3-font-family); display: flex; position: fixed;
}

/* -----------------------------------------
 * 侧边栏布局
 * ----------------------------------------- */
// 侧边栏容器
.media-player-sidebar {
  display: flex; flex-direction: column; height: 100%; overflow: hidden;
  
  &-content {
    flex: 1; overflow: hidden; position: relative;
    > div { position: absolute; inset: 0; overflow: hidden; }
  }
}

// 通用面板样式
.panel {
  @extend %panel-base;
  
  // 头部区域
  .panel-header {
    height: size(header); padding: 0 16px; border-bottom: 1px solid color(surface-lighter); @include flex(row, space-between, center);

    .panel-nav { @include flex(row, flex-start, center, 8px);
      h3 { margin: 0; font-size: 12px; cursor: pointer; transition: all 0.2s; color: color(on-surface); opacity: 0.65;
        &.active { font-size: 16px; color: color(primary); opacity: 1; }
        &:hover:not(.active) { color: color(primary); opacity: 0.85; }
      }
    }

    .header-controls, .panel-count { font-size: 12px; color: color(on-surface); opacity: 0.86; }
    .header-controls { @include flex(row, flex-start, center, 8px);
      .view-mode-btn { @extend %button-base; padding: 4px; color: color(on-surface); opacity: 0.7; @include flex(row, center, center); transition: opacity 0.2s; &:hover { opacity: 1; }
        svg { fill: currentColor; }
      }
    }
  }
  
  // 标签区域
  .panel-tabs {
    min-height: size(tabs); padding: size(spacing); @include flex(row, flex-start, center, size(spacing));
    border-bottom: 1px solid color(border); background: color(surface); flex-wrap: wrap;
    .tab {
      @extend %button-base; padding: 4px 12px; @include bordered; color: color(on-surface);
      white-space: nowrap; font-size: 14px; line-height: 20px;
      &:hover:not(.active) { border-color: color(primary); color: color(primary); }
      &.active { background: color(primary); color: #fff; border-color: color(primary); }
    }
  }
  
  // 内容区域
  .panel-content {
    @extend %section-container; padding-bottom: 60px;
    .panel-empty { height: 100%; @include flex(row, center, center); color: color(on-surface); opacity: 0.5; }
    .panel-items { @include flex(column, flex-start, stretch, 8px); }
    &.grid-view .panel-items {
      display: grid; grid-template-columns: repeat(auto-fill, minmax(130px, 1fr)); gap: 8px; align-items: start;
      &.grid-single { grid-template-columns: 100%; }
      .panel-item { padding: 0; margin: 0; height: auto; }
    }
  }
  
  // 底部区域
  .panel-footer {
    position: absolute; left: 50%; bottom: 16px; transform: translateX(-50%);
    @include flex(row, center, center, 8px); z-index: 10;
    .add-btn, .action-btn {
      @include flex(row, center, center); height: 28px; padding: 0 16px; border: none;
      border-radius: 24px; font-size: 14px; background: color(primary); color: color(on-primary);
      transition: all 0.2s; &:hover { filter: brightness(0.9); transform: translateY(-2px); }
      .icon { width: 14px; height: 14px; margin-right: 4px; fill: currentColor; }
      &:has(svg:only-child) { width: 28px; padding: 0; border-radius: 50%; justify-content: center; .icon { margin: 0; } }
    }
  }
  
  // 表单元素
  .tab-input, .select-wrapper, .input-wrapper, .player-path-input { @extend %input-base; }
  .select-wrapper { padding: 0 8px; cursor: pointer; }
  
  // 媒体卡片
  .filter-results {
    padding: size(spacing); height: 100%; overflow-y: auto;
    .media-card {
      position: relative; border-radius: 8px; overflow: hidden; min-height: 80px; margin-bottom: 12px;
      display: block; text-decoration: none; color: inherit; cursor: pointer;
      img { position: absolute; inset: 0; width: 100%; height: 100%; object-fit: cover; }
      .card-overlay { position: absolute; inset: 0; @include flex(column, flex-end, flex-start); padding: 8px; }
      .media-info { @include flex(row, flex-start, center, 4px); }
      span { font: 700 14px/1 sans-serif; color: white; text-shadow: 0 1px 3px rgba(0,0,0,0.7); }
    }
  }



  // 隐藏状态
  &.hidden { @extend %hidden-state; }
}

/* -----------------------------------------
 * 媒体标签统一样式
 * ----------------------------------------- */
.meta-tag {
  display: inline-block; padding: 2px 6px; margin-right: 4px; border-radius: 3px;
  font: 500 11px/1; text-transform: uppercase; letter-spacing: 0.5px;
  border: 1px solid; background: color-mix(in srgb, var(--tag-color) 10%, transparent);
  color: var(--tag-color); border-color: color-mix(in srgb, var(--tag-color) 20%, transparent);
  
  // 颜色映射
  &[data-source="B站"] { --tag-color: #fb7185; }
  &[data-source="本地"] { --tag-color: #22c55e; }
  &[data-source="OpenList"] { --tag-color: #f97316; }
  &[data-source="WebDAV"] { --tag-color: #10b981; }
  &[data-source="普通"] { --tag-color: #3b82f6; }
  &[data-source="标签"] { --tag-color: #8b5cf6; }
  &[data-source="思源"] { --tag-color: #06b6d4; }
  &[data-type="视频"] { --tag-color: #9333ea; }
  &[data-type="音频"] { --tag-color: #06b6d4; }
  &[data-type="文件夹"] { --tag-color: #eab308; }
  
  // 视图特定样式
  .item-tags &, .grid-tags & { font-size: 10px; padding: 1px 4px; }
  .grid-tags & { background: rgba(0, 0, 0, 0.7); color: #fff; border: none; }
}

/* -----------------------------------------
 * 列表项组件 & 视图样式
 * ----------------------------------------- */
.panel-item {
  @include flex(column, flex-start, stretch, 8px); padding: 12px; @extend %item-base;
  
  // 通用元素
  .item-content { @include flex(row, flex-start, stretch, 12px); }
  .item-thumbnail { @include thumbnail; }
  .item-title { @extend %item-title; }
  
  // 视图特定布局
  &.compact {
    padding: 8px 12px; @include flex(column, flex-start, stretch, 4px);
    .item-title { margin: 0; font-size: 14px; @include text-ellipsis; }
    .item-tags { @include flex(row, flex-end, center, 4px); align-self: flex-end; }
  }
  
  &.grid .item-thumbnail {
    @include thumbnail(100%, auto); aspect-ratio: 16/9;
    .item-title { @include position-absolute(null, 0, 0, 0); padding: 4px 8px; background: rgba(0, 0, 0, 0.7); color: #fff; @include text-ellipsis; }
    .duration { right: 4px; bottom: 24px; }
    .grid-tags { @include position-absolute(4px, null, null, 4px); @include flex(column, flex-start, flex-start, 2px); }
  }
  
  // 状态样式
  &.playing .item-info .item-title { color: color(primary); }
  
  // 详细视图信息区
  .item-info {
    flex: 1; min-width: 0; @include flex(column, flex-start, stretch, 4px);
    
    .item-artist {
      @include flex(row, flex-start, center, 6px); font-size: 12px; color: color(on-surface); @include text-ellipsis;
      .artist-icon { width: 16px; height: 16px; border-radius: 50%; }
    }
    
    .item-meta { @include flex(row, flex-start, center, 4px); font-size: 12px; color: color(on-surface-variant); margin: 4px 0; }
    .item-url { @include text-ellipsis; font-size: 12px; color: color(on-surface-variant); opacity: 0.6; }
  }
}

// 分P列表 & 字幕项
.item-parts {
  display: grid; gap: 4px; width: 100%; margin-top: 4px; grid-template-columns: repeat(auto-fill, minmax(28px, 1fr));
  &.parts-list { display: flex; flex-direction: column; gap: 2px;
    .part-item, .parts-toggle { width: 100%; height: auto; padding: 4px 8px; justify-content: flex-start; text-align: left; }
  }
  .part-item, .parts-toggle {
    width: 28px; height: 28px; min-width: 0; font-size: 12px; @include flex(row, center, center); @include bordered; background: color(surface); color: var(--b3-theme-on-background); transition: all 0.2s; cursor: pointer;
    &:hover { border-color: color(primary); color: color(primary); }
  }
  .part-item.playing { background: color(primary); border-color: color(primary); color: #fff; }
  .parts-toggle { opacity: 0.6; }
}

.subtitle-item {
  @include flex(row, flex-start, center); padding: 8px 12px; @extend %item-base; margin-bottom: 4px;
  &.current { background: color(primary-lighter); border-color: color(primary); }
  .subtitle-time { flex-shrink: 0; width: 52px; color: color(primary); font-weight: 500; }
  .subtitle-text { flex: 1; line-height: 1.4; word-break: break-word; }
  .action-btn { @extend %button-base; padding: 4px; opacity: 0.6; &:hover { opacity: 1; } .icon { width: 14px; height: 14px; } }
}

/* -----------------------------------------
 * 设置界面
 * ----------------------------------------- */
// 设置项
.setting-item {
  margin-bottom: 8px; padding: 16px; @extend %item-base;
  @include flex(row, space-between, center, 16px);
  
  .setting-info {
    flex: 1; min-width: 0;
    .setting-title { @include standard-text(14px, 500); }
    .setting-description { font-size: 12px; color: color(on-surface); margin-top: 4px; }
    .setting-content { margin-top: 12px; }
  }
  
  .setting-control { @include flex(row, flex-start, center, 8px); }
}

// 开关样式
.checkbox-wrapper {
  position: relative; width: 40px; height: 20px;
  input[type="checkbox"] { opacity: 0; width: 0; height: 0; 
    &:checked + .checkbox-custom { background: color(primary);
      &:after { transform: translateX(20px); }
    }
  }
  .checkbox-custom {
    position: absolute; top: 0; left: 0; width: 40px; height: 20px;
    background: color(surface-lighter); border-radius: 10px; transition: all 0.2s; cursor: pointer;
    &:after { content: ''; position: absolute; top: 2px; left: 2px;
      width: 16px; height: 16px; background: #fff; border-radius: 50%; transition: all 0.2s;
    }
  }
}

// 滑块样式
.slider-wrapper { width: 100%; display: flex; align-items: center; gap: 8px; }

.slider-wrapper input[type="range"] {
  flex: 1; height: 4px; background: color(surface-lighter); border-radius: 2px; cursor: pointer;
  &::-webkit-slider-thumb {
    width: 12px; height: 12px; border-radius: 50%; background: color(primary);
    cursor: pointer; transition: transform 0.2s; &:hover { transform: scale(1.2); }
  }
}
.slider-value { min-width: 40px; text-align: right; color: color(on-surface); }

// 账号描述样式
.acc-desc {
  display: flex; gap: 10px;
  .acc-icon { width: 48px; height: 48px; border-radius: 50%; flex-shrink: 0; fill: color(primary); }
  .acc-info { line-height: 1.5; b { font-size: 14px; } small { color: #888; } .acc-muted { font-size: 11px; color: #aaa; } }
}

// 文本区域
.setting-item-textarea .clear-icon, .setting-item-text .clear-icon {
  position: absolute; right: 8px; top: 8px; cursor: pointer; opacity: 0.5;
  .icon { width: 16px; height: 16px; } &:hover { opacity: 0.8; }
}

// 辅助元素
.setting-label { @include standard-text(14px, 500); margin-bottom: 4px; }
.openlist-path-nav { 
  padding: 4px 12px; font-size: 12px; 
  .path-item { background: none; border: none; color: color(primary); cursor: pointer; padding: 0; } 
  .path-sep { margin: 0 4px; opacity: 0.6; } 
}

// 图片画廊样式
.image-gallery {
  display: flex; gap: 10px; margin-top: 10px;
  .image-item { width: 200px; height: 200px; object-fit: contain; border-radius: 8px; }
}